import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, Use<PERSON>, <PERSON>, Trophy } from 'lucide-react';
import ProfileCreation from './components/ProfileCreation';
import TopicSelection from './components/TopicSelection';
import QuizInterface from './components/QuizInterface';
import VoiceManager from './components/VoiceManager';

export interface Profile {
  id: string;
  name: string;
  age: number;
  interests: string[];
}

export interface QuizSession {
  id: string;
  profile_id: string;
  topic: string;
  questions: Question[];
  current_question: number;
  score: number;
  answers: Answer[];
}

export interface Question {
  question: string;
  options: string[];
  correct_answer: number;
  explanation: string;
}

export interface Answer {
  question_index: number;
  answer_index: number;
  is_correct: boolean;
}

type AppState = 'profile' | 'topic' | 'quiz' | 'results';

function App() {
  const [currentState, setCurrentState] = useState<AppState>('profile');
  const [profile, setProfile] = useState<Profile | null>(null);
  const [currentTopic, setCurrentTopic] = useState<string>('');
  const [quizSession, setQuizSession] = useState<QuizSession | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Initialize voice manager
  const voiceManager = new VoiceManager();

  useEffect(() => {
    // Welcome message when app loads
    voiceManager.speak("Welcome to the AI Quiz Toy! Let's start by creating your profile.");
  }, []);

  const handleProfileCreated = (newProfile: Profile) => {
    setProfile(newProfile);
    setCurrentState('topic');
    voiceManager.speak(`Great to meet you, ${newProfile.name}! Now let's choose a topic for your quiz.`);
  };

  const handleTopicSelected = async (topic: string) => {
    if (!profile) return;
    
    setCurrentTopic(topic);
    setIsLoading(true);
    voiceManager.speak(`Excellent choice! I'm creating a fun quiz about ${topic} just for you. This will only take a moment.`);

    try {
      const response = await fetch('http://localhost:5000/api/quiz/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          topic: topic,
          profile_id: profile.id,
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        // Fetch the session data
        const sessionResponse = await fetch(`http://localhost:5000/api/quiz/session/${data.session_id}`);
        const sessionData = await sessionResponse.json();
        
        if (sessionData.success) {
          setQuizSession(sessionData.session);
          setCurrentState('quiz');
          voiceManager.speak(`Your quiz is ready! Let's begin with the first question.`);
        }
      } else {
        throw new Error(data.error || 'Failed to generate quiz');
      }
    } catch (error) {
      console.error('Error generating quiz:', error);
      voiceManager.speak("I'm sorry, there was a problem creating your quiz. Let's try a different topic.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleQuizComplete = (finalScore: number, totalQuestions: number) => {
    // Update the quiz session with final score before showing results
    setQuizSession(prev => prev ? {
      ...prev,
      score: finalScore,
      current_question: totalQuestions
    } : null);

    setCurrentState('results');
    const percentage = Math.round((finalScore / totalQuestions) * 100);
    voiceManager.speak(`Congratulations! You completed the quiz with ${finalScore} out of ${totalQuestions} correct answers. That's ${percentage} percent! You did amazing!`);
  };

  const handleScoreUpdate = (currentScore: number, currentQuestion: number) => {
    // Update the quiz session state in App component to keep header in sync
    setQuizSession(prev => prev ? {
      ...prev,
      score: currentScore,
      current_question: currentQuestion
    } : null);
  };

  const handlePlayAgain = () => {
    setCurrentState('topic');
    setQuizSession(null);
    setCurrentTopic('');
    voiceManager.speak("Let's choose a new topic for another fun quiz!");
  };

  const handleNewProfile = () => {
    setCurrentState('profile');
    setProfile(null);
    setQuizSession(null);
    setCurrentTopic('');
    voiceManager.speak("Let's create a new profile and start fresh!");
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-200 via-purple-200 via-blue-200 to-green-200 relative overflow-hidden">
      {/* Floating decorative elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-10 left-10 w-8 h-8 bg-yellow-300 rounded-full star-sparkle opacity-70"></div>
        <div className="absolute top-32 right-20 w-6 h-6 bg-pink-400 rounded-full star-sparkle opacity-60" style={{animationDelay: '0.5s'}}></div>
        <div className="absolute bottom-40 left-16 w-10 h-10 bg-blue-400 rounded-full star-sparkle opacity-50" style={{animationDelay: '1s'}}></div>
        <div className="absolute bottom-20 right-32 w-7 h-7 bg-green-400 rounded-full star-sparkle opacity-65" style={{animationDelay: '1.5s'}}></div>
        <div className="absolute top-1/2 left-1/4 w-5 h-5 bg-purple-400 rounded-full star-sparkle opacity-55" style={{animationDelay: '2s'}}></div>
      </div>

      {/* Header */}
      <header className="bg-gradient-to-r from-pink-300/80 via-purple-300/80 to-blue-300/80 backdrop-blur-sm shadow-lg sticky top-0 z-10 border-b-4 border-white/50">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-r from-yellow-400 via-pink-500 to-purple-500 rounded-2xl shadow-lg wiggle">
                <Sparkles className="w-8 h-8 text-white" />
              </div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-700 via-pink-600 to-orange-500 bg-clip-text text-transparent">
                🌟 AI Quiz Adventure 🌟
              </h1>
            </div>
            
            {profile && (
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2 bg-white/80 rounded-full px-6 py-3 shadow-lg border-2 border-pink-200">
                  <User className="w-5 h-5 text-purple-600" />
                  <span className="text-lg font-semibold text-gray-800">👋 {profile.name}</span>
                </div>
                {quizSession && (
                  <div className="flex items-center space-x-2 bg-gradient-to-r from-yellow-300 to-orange-300 rounded-full px-6 py-3 shadow-lg border-2 border-yellow-400 celebration-bounce">
                    <Trophy className="w-5 h-5 text-orange-700" />
                    <span className="text-lg font-bold text-orange-800">
                      🏆 {quizSession.score} points!
                    </span>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 py-8">
        {isLoading && (
          <div className="fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-50">
            <div className="kid-card-rainbow p-10 max-w-sm w-full mx-4 text-center bounce-in">
              <div className="relative">
                <div className="animate-spin w-16 h-16 border-4 border-pink-200 border-t-pink-500 rounded-full mx-auto mb-6"></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <Sparkles className="w-6 h-6 text-pink-500 star-sparkle" />
                </div>
              </div>
              <p className="text-xl font-bold text-gray-800 mb-2">🎨 Creating your magical quiz...</p>
              <p className="text-lg text-gray-600">✨ This will only take a moment! ✨</p>
            </div>
          </div>
        )}

        {currentState === 'profile' && (
          <ProfileCreation 
            onProfileCreated={handleProfileCreated}
            voiceManager={voiceManager}
          />
        )}

        {currentState === 'topic' && profile && (
          <TopicSelection 
            profile={profile}
            onTopicSelected={handleTopicSelected}
            voiceManager={voiceManager}
          />
        )}

        {currentState === 'quiz' && quizSession && (
          <QuizInterface
            session={quizSession}
            onQuizComplete={handleQuizComplete}
            onScoreUpdate={handleScoreUpdate}
            voiceManager={voiceManager}
          />
        )}

        {currentState === 'results' && quizSession && (
          <div className="text-center space-y-8">
            <div className="kid-card-rainbow p-10 bounce-in">
              {/* Celebration stars */}
              <div className="flex justify-center space-x-4 mb-6">
                <div className="text-4xl star-sparkle">⭐</div>
                <div className="text-5xl star-sparkle" style={{animationDelay: '0.2s'}}>🌟</div>
                <div className="text-4xl star-sparkle" style={{animationDelay: '0.4s'}}>⭐</div>
              </div>

              <div className="w-24 h-24 bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-6 celebration-bounce shadow-xl">
                <Trophy className="w-12 h-12 text-white" />
              </div>

              <h2 className="text-4xl font-bold text-gray-800 mb-6">
                🎉 Amazing job, {profile?.name}! 🎉
              </h2>

              <div className="text-8xl font-bold text-transparent bg-gradient-to-r from-purple-600 via-pink-500 to-orange-500 bg-clip-text mb-6 celebration-bounce">
                {quizSession.score}/{quizSession.questions.length}
              </div>

              <div className="bg-gradient-to-r from-green-400 to-blue-500 text-white rounded-2xl p-6 mb-6 shadow-lg">
                <p className="text-2xl font-bold mb-2">
                  🏆 {Math.round((quizSession.score / quizSession.questions.length) * 100)}% Correct! 🏆
                </p>
                <p className="text-lg">
                  You're a {currentTopic} superstar! ⭐
                </p>
              </div>

              <p className="text-xl text-gray-700 mb-8">
                🎓 You learned so much about <span className="font-bold text-purple-600 bg-yellow-200 px-2 py-1 rounded-lg">{currentTopic}</span>! 🎓
              </p>

              <div className="flex flex-col sm:flex-row gap-6 justify-center">
                <button
                  onClick={handlePlayAgain}
                  className="kid-button-primary text-xl"
                >
                  <Brain className="w-6 h-6 inline mr-3" />
                  🚀 Play Again!
                </button>
                <button
                  onClick={handleNewProfile}
                  className="kid-button-secondary text-xl"
                >
                  <User className="w-6 h-6 inline mr-3" />
                  👤 New Friend
                </button>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
}

export default App;