Stack trace:
Frame         Function      Args
0007FFFFABB0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF9AB0) msys-2.0.dll+0x1FE8E
0007FFFFABB0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFAE88) msys-2.0.dll+0x67F9
0007FFFFABB0  000210046832 (000210286019, 0007FFFFAA68, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFABB0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFABB0  000210068E24 (0007FFFFABC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFAE90  00021006A225 (0007FFFFABC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFCB0E60000 ntdll.dll
7FFCAFA80000 KERNEL32.DLL
7FFCAE330000 KERNELBASE.dll
7FFCAFB50000 USER32.dll
7FFCAE720000 win32u.dll
7FFCB0DF0000 GDI32.dll
7FFCAE750000 gdi32full.dll
7FFCAE130000 msvcp_win.dll
7FFCAE1E0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFCB0D00000 advapi32.dll
7FFCAF4C0000 msvcrt.dll
7FFCB07D0000 sechost.dll
7FFCB09F0000 RPCRT4.dll
7FFCAD6D0000 CRYPTBASE.DLL
7FFCAEB60000 bcryptPrimitives.dll
7FFCB0930000 IMM32.DLL
