@import url('https://fonts.googleapis.com/css2?family=Fredoka:wght@300;400;500;600;700&family=Comic+Neue:wght@300;400;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    font-family: 'Fredoka', 'Comic Neue', cursive, sans-serif;
  }
}

@layer components {
  .kid-button {
    @apply px-6 py-4 text-lg font-semibold rounded-2xl shadow-lg transform transition-all duration-200 hover:scale-105 active:scale-95;
  }

  .kid-button-primary {
    @apply kid-button bg-gradient-to-r from-pink-400 via-purple-500 to-indigo-500 text-white hover:shadow-xl;
  }

  .kid-button-secondary {
    @apply kid-button bg-gradient-to-r from-yellow-400 via-orange-400 to-red-400 text-white hover:shadow-xl;
  }

  .kid-button-success {
    @apply kid-button bg-gradient-to-r from-green-400 via-emerald-500 to-teal-500 text-white hover:shadow-xl;
  }

  .kid-card {
    @apply bg-white/90 backdrop-blur-sm rounded-3xl shadow-xl border-4 border-transparent bg-clip-padding;
  }

  .kid-card-rainbow {
    @apply kid-card;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3) border-box;
  }

  .bounce-in {
    animation: bounceIn 0.6s ease-out;
  }

  .wiggle {
    animation: wiggle 0.5s ease-in-out;
  }

  .star-sparkle {
    animation: sparkle 1.5s ease-in-out infinite;
  }

  .celebration-bounce {
    animation: celebrationBounce 0.8s ease-out;
  }

  .slide-in-left {
    animation: slideInLeft 0.6s ease-out;
  }

  .slide-in-right {
    animation: slideInRight 0.6s ease-out;
  }

  .slide-in-up {
    animation: slideInUp 0.6s ease-out;
  }

  .fade-in {
    animation: fadeIn 0.8s ease-out;
  }

  .scale-in {
    animation: scaleIn 0.5s ease-out;
  }

  .float {
    animation: float 3s ease-in-out infinite;
  }

  .pulse-glow {
    animation: pulseGlow 2s ease-in-out infinite;
  }

  .rainbow-border {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3);
    background-size: 400% 400%;
    animation: rainbowShift 3s ease infinite;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3) rotate(-10deg);
    opacity: 0;
  }
  50% {
    transform: scale(1.05) rotate(2deg);
  }
  70% {
    transform: scale(0.9) rotate(-1deg);
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

@keyframes wiggle {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(-3deg); }
  75% { transform: rotate(3deg); }
}

@keyframes sparkle {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
  50% {
    transform: scale(1.2) rotate(180deg);
    opacity: 0.8;
  }
}

@keyframes celebrationBounce {
  0% {
    transform: translateY(0) scale(1);
  }
  30% {
    transform: translateY(-20px) scale(1.1);
  }
  60% {
    transform: translateY(-10px) scale(1.05);
  }
  100% {
    transform: translateY(0) scale(1);
  }
}

@keyframes slideInLeft {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInUp {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes scaleIn {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.4);
  }
  50% {
    box-shadow: 0 0 40px rgba(168, 85, 247, 0.8);
  }
}

@keyframes rainbowShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
